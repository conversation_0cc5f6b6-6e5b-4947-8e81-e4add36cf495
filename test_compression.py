#!/usr/bin/env python3
"""
Test script for RF-DETR model compression functionality.

This script demonstrates how to use the model compression tools and validates
that the compressed models work correctly with the existing inference pipeline.
"""

import os
import sys
import torch
import numpy as np
from PIL import Image
import tempfile
import shutil

# Import our compression modules
from model_compression import (
    ModelCompressionConfig, 
    RFDETRPruner, 
    RFDETRDistiller, 
    ModelEvaluator,
    create_dummy_dataloader,
    export_to_onnx
)
from rf_detr import RTDETR_ONNX


def create_test_model(save_path: str) -> None:
    """Create a simple test model for compression testing."""
    print("Creating test model...")
    
    class TestRFDETR(torch.nn.Module):
        def __init__(self):
            super().__init__()
            # Simplified RF-DETR-like architecture
            self.backbone = torch.nn.Sequential(
                torch.nn.Conv2d(3, 64, 3, padding=1),
                torch.nn.ReLU(),
                torch.nn.Conv2d(64, 128, 3, padding=1),
                torch.nn.ReLU(),
                torch.nn.Conv2d(128, 256, 3, padding=1),
                torch.nn.ReLU(),
                torch.nn.AdaptiveAvgPool2d((14, 14))
            )
            
            self.neck = torch.nn.Sequential(
                torch.nn.Conv2d(256, 256, 1),
                torch.nn.ReLU(),
                torch.nn.Conv2d(256, 256, 1)
            )
            
            # Detection head
            self.bbox_head = torch.nn.Linear(256 * 14 * 14, 4 * 100)  # 100 queries, 4 coords
            self.class_head = torch.nn.Linear(256 * 14 * 14, 80 * 100)  # 100 queries, 80 classes
            
        def forward(self, x):
            # Backbone
            features = self.backbone(x)
            
            # Neck
            features = self.neck(features)
            
            # Flatten for heads
            features_flat = features.view(features.size(0), -1)
            
            # Detection heads
            bbox_pred = self.bbox_head(features_flat).view(-1, 100, 4)
            class_pred = self.class_head(features_flat).view(-1, 100, 80)
            
            return {
                'pred_boxes': bbox_pred,
                'pred_logits': class_pred
            }
    
    # Create model and save
    model = TestRFDETR()
    
    # Create a checkpoint-like structure similar to the real RF-DETR
    checkpoint = {
        'model': model.state_dict(),
        'optimizer': None,
        'lr_scheduler': None,
        'epoch': 0,
        'args': None
    }
    
    torch.save(checkpoint, save_path)
    print(f"Test model saved to {save_path}")


def test_pruning(model_path: str, output_dir: str) -> str:
    """Test model pruning functionality."""
    print("\n" + "="*50)
    print("TESTING MODEL PRUNING")
    print("="*50)
    
    config = ModelCompressionConfig()
    config.pruning_ratio = 0.3
    config.device = 'cpu'  # Use CPU for testing
    
    pruner = RFDETRPruner(config)
    
    # Test pruning
    pruned_path = os.path.join(output_dir, "test_model_pruned.pth")
    pruned_model, stats = pruner.prune_model(model_path, pruned_path)
    
    print(f"✓ Pruning completed successfully")
    print(f"  - Original parameters: {stats['original_parameters']:,}")
    print(f"  - Pruned parameters: {stats['pruned_parameters']:,}")
    print(f"  - Compression ratio: {stats['compression_ratio']:.3f}")
    
    return pruned_path


def test_knowledge_distillation(model_path: str, output_dir: str) -> str:
    """Test knowledge distillation functionality."""
    print("\n" + "="*50)
    print("TESTING KNOWLEDGE DISTILLATION")
    print("="*50)
    
    config = ModelCompressionConfig()
    config.student_scale_factor = 0.5
    config.kd_epochs = 5  # Reduced for testing
    config.device = 'cpu'  # Use CPU for testing
    config.batch_size = 2  # Small batch for testing
    
    distiller = RFDETRDistiller(config)
    
    # Create student model
    student_model = distiller.create_student_model(model_path)
    print(f"✓ Student model created with scale factor {config.student_scale_factor}")
    
    # Create dummy training data
    train_loader = create_dummy_dataloader(config)
    print(f"✓ Training data loader created with {len(train_loader)} batches")
    
    # Train student model
    student_path = os.path.join(output_dir, "test_model_student.pth")
    training_stats = distiller.train_student(model_path, student_model, train_loader, student_path)
    
    print(f"✓ Knowledge distillation completed")
    print(f"  - Training epochs: {training_stats['epochs']}")
    print(f"  - Final loss: {training_stats['losses'][-1]:.4f}")
    
    return student_path


def test_onnx_export(pytorch_path: str, output_dir: str) -> str:
    """Test ONNX export functionality."""
    print("\n" + "="*50)
    print("TESTING ONNX EXPORT")
    print("="*50)
    
    onnx_path = os.path.join(output_dir, "test_model.onnx")
    
    try:
        export_to_onnx(pytorch_path, onnx_path, input_size=(560, 560))
        print(f"✓ ONNX export completed successfully")
        print(f"  - Output file: {onnx_path}")
        print(f"  - File size: {os.path.getsize(onnx_path) / (1024*1024):.2f} MB")
        return onnx_path
    except Exception as e:
        print(f"✗ ONNX export failed: {e}")
        return None


def test_model_evaluation(original_path: str, compressed_paths: list, output_dir: str):
    """Test model evaluation and comparison functionality."""
    print("\n" + "="*50)
    print("TESTING MODEL EVALUATION")
    print("="*50)
    
    config = ModelCompressionConfig()
    evaluator = ModelEvaluator(config)
    
    # Test model size calculation
    original_stats = evaluator.calculate_model_size(original_path)
    print(f"✓ Original model analysis:")
    print(f"  - File size: {original_stats['file_size_mb']:.2f} MB")
    print(f"  - Parameters: {original_stats['parameter_count']:,}")
    
    # Compare with compressed models
    for i, compressed_path in enumerate(compressed_paths):
        if compressed_path and os.path.exists(compressed_path):
            print(f"\n✓ Comparing with compressed model {i+1}:")
            try:
                comparison = evaluator.compare_models(original_path, compressed_path)
                improvements = comparison['improvements']
                print(f"  - Size reduction: {improvements['size_reduction_ratio']*100:.1f}%")
                print(f"  - Parameter reduction: {improvements['parameter_reduction_ratio']*100:.1f}%")
            except Exception as e:
                print(f"  ✗ Comparison failed: {e}")


def test_inference_compatibility(onnx_path: str):
    """Test that compressed ONNX model works with existing inference code."""
    print("\n" + "="*50)
    print("TESTING INFERENCE COMPATIBILITY")
    print("="*50)
    
    if not onnx_path or not os.path.exists(onnx_path):
        print("✗ No ONNX model available for inference testing")
        return
    
    try:
        # Create a test image
        test_image = Image.new('RGB', (640, 480), color='red')
        test_image_path = "test_image.jpg"
        test_image.save(test_image_path)
        
        # Try to load the model with existing ONNX inference code
        # Note: This might fail because our test model has a different structure
        # than the real RF-DETR, but it demonstrates the integration
        try:
            model = RTDETR_ONNX(onnx_path)
            print(f"✓ ONNX model loaded successfully")
            print(f"  - Input size: {model.input_width}x{model.input_height}")
            
            # Try inference (might fail due to model structure differences)
            try:
                scores, labels, boxes = model.run_inference(test_image_path)
                print(f"✓ Inference completed successfully")
                print(f"  - Detections: {len(boxes)}")
            except Exception as e:
                print(f"⚠ Inference failed (expected for test model): {e}")
                
        except Exception as e:
            print(f"⚠ Model loading failed (expected for test model): {e}")
        
        # Clean up
        if os.path.exists(test_image_path):
            os.remove(test_image_path)
            
    except Exception as e:
        print(f"✗ Inference compatibility test failed: {e}")


def main():
    """Run all compression tests."""
    print("RF-DETR Model Compression Test Suite")
    print("="*60)
    
    # Create temporary directory for test outputs
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"Using temporary directory: {temp_dir}")
        
        # Step 1: Create test model
        test_model_path = os.path.join(temp_dir, "test_model.pth")
        create_test_model(test_model_path)
        
        # Step 2: Test pruning
        try:
            pruned_path = test_pruning(test_model_path, temp_dir)
        except Exception as e:
            print(f"✗ Pruning test failed: {e}")
            pruned_path = None
        
        # Step 3: Test knowledge distillation
        try:
            student_path = test_knowledge_distillation(test_model_path, temp_dir)
        except Exception as e:
            print(f"✗ Knowledge distillation test failed: {e}")
            student_path = None
        
        # Step 4: Test ONNX export
        try:
            onnx_path = test_onnx_export(test_model_path, temp_dir)
        except Exception as e:
            print(f"✗ ONNX export test failed: {e}")
            onnx_path = None
        
        # Step 5: Test model evaluation
        compressed_paths = [p for p in [pruned_path, student_path] if p]
        try:
            test_model_evaluation(test_model_path, compressed_paths, temp_dir)
        except Exception as e:
            print(f"✗ Model evaluation test failed: {e}")
        
        # Step 6: Test inference compatibility
        try:
            test_inference_compatibility(onnx_path)
        except Exception as e:
            print(f"✗ Inference compatibility test failed: {e}")
        
        print("\n" + "="*60)
        print("TEST SUITE COMPLETED")
        print("="*60)
        print("Note: Some tests may show warnings or expected failures")
        print("when using simplified test models instead of real RF-DETR models.")


if __name__ == "__main__":
    main()
