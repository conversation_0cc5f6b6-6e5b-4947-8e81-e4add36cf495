# RF-DETR Model Compression Guide

This guide explains how to use the `model_compression.py` script to compress RF-DETR models for edge deployment using pruning and knowledge distillation techniques.

## Overview

The compression script implements two main strategies:

1. **Structured Pruning**: Removes entire channels/filters while maintaining model structure for hardware efficiency
2. **Knowledge Distillation**: Trains a smaller student model using the original model as teacher

## Installation

First, install the required dependencies:

```bash
pip install torch torchvision onnx onnxruntime numpy pillow
```

For knowledge distillation training, you may also want:
```bash
pip install matplotlib tensorboard  # For visualization (optional)
```

## Quick Start

### 1. Model Pruning

Prune your RF-DETR model to reduce size by removing less important channels:

```bash
# Basic pruning with 30% channel reduction
python model_compression.py --method pruning --input rf-detr-base.pth --output rf-detr-pruned.pth

# Custom pruning ratio (50% reduction)
python model_compression.py --method pruning --input rf-detr-base.pth --output rf-detr-pruned.pth --pruning-ratio 0.5

# Specify pruning method
python model_compression.py --method pruning --input rf-detr-base.pth --output rf-detr-pruned.pth --pruning-method magnitude
```

### 2. Knowledge Distillation

Train a smaller student model using the original model as teacher:

```bash
# Basic knowledge distillation with 50% model scaling
python model_compression.py --method distillation --input rf-detr-base.pth --output rf-detr-student.pth

# Custom student scaling and training parameters
python model_compression.py --method distillation \
    --input rf-detr-base.pth \
    --output rf-detr-student.pth \
    --student-scale 0.3 \
    --kd-epochs 100 \
    --kd-alpha 0.8
```

### 3. Export to ONNX

Convert compressed PyTorch models to ONNX format:

```bash
# Export pruned model to ONNX
python model_compression.py --method export --input rf-detr-pruned.pth --output rf-detr-pruned.onnx

# Export student model to ONNX
python model_compression.py --method export --input rf-detr-student.pth --output rf-detr-student.onnx
```

### 4. Model Comparison

Compare original and compressed models:

```bash
# Compare original vs pruned model
python model_compression.py --method compare --input rf-detr-base.pth --compare-with rf-detr-pruned.pth

# Compare original vs student model
python model_compression.py --method compare --input rf-detr-base.pth --compare-with rf-detr-student.pth
```

## Configuration Parameters

### Pruning Parameters

- `--pruning-ratio`: Percentage of channels to remove (0.0-0.9, default: 0.3)
- `--pruning-method`: Method for selecting channels to prune
  - `magnitude`: Remove channels with smallest L1 norm (recommended)
  - `random`: Remove channels randomly

### Knowledge Distillation Parameters

- `--student-scale`: Scale factor for student model size (default: 0.5)
- `--kd-epochs`: Number of training epochs (default: 50)
- `--kd-alpha`: Weight for distillation loss vs task loss (default: 0.7)
- `--batch-size`: Training batch size (default: 4)

### General Parameters

- `--device`: Computing device (`cpu`, `cuda`, or `auto`)
- `--input`: Input model path (required)
- `--output`: Output model path (auto-generated if not specified)

## Understanding the Output

### Pruning Results

```
MODEL COMPRESSION REPORT
============================================================
Pruning Results:
  Original Parameters: 42,123,456
  Pruned Parameters: 29,486,419
  Compression Ratio: 0.300
  Size Reduction: 30.00%
  Pruning Method: magnitude
============================================================
```

### Model Comparison

```
Model Comparison:
  Original Model Size: 161.23 MB
  Compressed Model Size: 112.86 MB
  Size Reduction: 30.0%
  Parameter Reduction: 30.0%

Inference Speed:
  Original FPS: 18.5
  Compressed FPS: 24.2
  Speed Change: +30.8%
```

## Advanced Usage

### Custom Configuration

You can modify the `ModelCompressionConfig` class in the script to customize:

- Input image size
- Number of classes
- Confidence thresholds
- Temperature for knowledge distillation
- Preservation of specific layers

### Batch Processing

Process multiple models:

```bash
#!/bin/bash
for model in rf-detr-*.pth; do
    echo "Processing $model"
    python model_compression.py --method pruning --input "$model" --pruning-ratio 0.3
    python model_compression.py --method export --input "${model%.pth}_pruned.pth"
done
```

## Best Practices

### 1. Pruning Guidelines

- Start with conservative pruning ratios (0.2-0.3) and increase gradually
- Always preserve first and last layers for stability
- Use magnitude-based pruning for better accuracy retention
- Test accuracy after each pruning step

### 2. Knowledge Distillation Tips

- Use temperature scaling (T=4-6) for better soft target generation
- Balance distillation loss and task loss with alpha parameter
- Train for sufficient epochs to allow convergence
- Use learning rate scheduling for better results

### 3. Edge Deployment Considerations

- Target model size < 50MB for mobile deployment
- Aim for > 30 FPS on target hardware
- Consider quantization as additional compression step
- Test on actual edge devices before deployment

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**: Reduce batch size or use CPU
2. **Model Loading Errors**: Ensure PyTorch version compatibility
3. **ONNX Export Failures**: Check model architecture compatibility

### Performance Tips

- Use GPU for faster training and inference
- Enable mixed precision training for larger models
- Use data parallelism for multi-GPU setups

## Integration with Existing Workflow

### With Original RF-DETR Code

```python
from rf_detr import RTDETR_ONNX
from model_compression import ModelEvaluator, ModelCompressionConfig

# Load compressed model
compressed_model = RTDETR_ONNX("rf-detr-pruned.onnx")

# Evaluate performance
config = ModelCompressionConfig()
evaluator = ModelEvaluator(config)
comparison = evaluator.compare_models("rf-detr-base.onnx", "rf-detr-pruned.onnx")
```

### Custom Training Data

For better knowledge distillation results, replace the dummy dataset with real training data:

```python
class CustomDataset(Dataset):
    def __init__(self, image_paths, annotations):
        self.image_paths = image_paths
        self.annotations = annotations
    
    def __getitem__(self, idx):
        # Load and preprocess real images
        image = load_image(self.image_paths[idx])
        annotation = self.annotations[idx]
        return image, annotation
```

## License

This compression script is provided under the MIT License, compatible with the existing RF-DETR codebase licensing.

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Verify your model files and paths
3. Ensure all dependencies are installed correctly
4. Test with the provided example commands first

## Future Enhancements

Planned improvements:
- Quantization support (INT8/FP16)
- Automatic hyperparameter tuning
- Multi-objective optimization (size vs accuracy vs speed)
- Support for additional model architectures
