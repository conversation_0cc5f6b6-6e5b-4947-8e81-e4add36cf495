#!/usr/bin/env python3
"""
Demonstration script showing how to use compressed RF-DETR models.

This script shows how to:
1. Load and use compressed ONNX models
2. Compare performance between original and compressed models
3. Integrate compressed models into existing workflows

Usage:
    python compressed_model_demo.py --image data/1744093833400000000.png
"""

import argparse
import time
import os
from typing import Dict, List, <PERSON><PERSON>
import numpy as np
from PIL import Image

# Import existing RF-DETR inference code
from rf_detr import RTDETR_ONNX

# Import compression utilities
from model_compression import ModelEvaluator, ModelCompressionConfig


class CompressedModelDemo:
    """Demonstration class for compressed RF-DETR models."""
    
    def __init__(self):
        self.config = ModelCompressionConfig()
        self.evaluator = ModelEvaluator(self.config)
        
    def load_models(self, original_path: str, compressed_path: str) -> Tuple[RTDETR_ONNX, RTDETR_ONNX]:
        """
        Load original and compressed ONNX models.
        
        Args:
            original_path: Path to original ONNX model
            compressed_path: Path to compressed ONNX model
            
        Returns:
            Tuple of (original_model, compressed_model)
        """
        print("Loading models...")
        
        try:
            original_model = RTDETR_ONNX(original_path)
            print(f"✓ Original model loaded: {original_path}")
        except Exception as e:
            print(f"✗ Failed to load original model: {e}")
            original_model = None
            
        try:
            compressed_model = RTDETR_ONNX(compressed_path)
            print(f"✓ Compressed model loaded: {compressed_path}")
        except Exception as e:
            print(f"✗ Failed to load compressed model: {e}")
            compressed_model = None
            
        return original_model, compressed_model
    
    def benchmark_inference(self, model: RTDETR_ONNX, image_path: str, 
                          num_runs: int = 50) -> Dict[str, float]:
        """
        Benchmark inference performance of a model.
        
        Args:
            model: ONNX model to benchmark
            image_path: Path to test image
            num_runs: Number of inference runs
            
        Returns:
            Performance metrics dictionary
        """
        print(f"Benchmarking model with {num_runs} runs...")
        
        # Warm up
        for _ in range(5):
            try:
                model.run_inference(image_path)
            except:
                pass
        
        # Benchmark
        times = []
        successful_runs = 0
        
        for i in range(num_runs):
            start_time = time.time()
            try:
                scores, labels, boxes = model.run_inference(image_path)
                end_time = time.time()
                times.append(end_time - start_time)
                successful_runs += 1
                
                if i == 0:  # Store first successful result
                    first_result = (scores, labels, boxes)
                    
            except Exception as e:
                if i == 0:
                    print(f"⚠ Inference failed: {e}")
                continue
        
        if successful_runs == 0:
            return {
                'avg_time_ms': 0,
                'fps': 0,
                'successful_runs': 0,
                'total_runs': num_runs
            }
        
        avg_time = np.mean(times)
        std_time = np.std(times)
        
        return {
            'avg_time_ms': avg_time * 1000,
            'std_time_ms': std_time * 1000,
            'min_time_ms': min(times) * 1000,
            'max_time_ms': max(times) * 1000,
            'fps': 1.0 / avg_time,
            'successful_runs': successful_runs,
            'total_runs': num_runs,
            'first_result': first_result if 'first_result' in locals() else None
        }
    
    def compare_inference_results(self, original_result: Tuple, compressed_result: Tuple) -> Dict:
        """
        Compare inference results between original and compressed models.
        
        Args:
            original_result: (scores, labels, boxes) from original model
            compressed_result: (scores, labels, boxes) from compressed model
            
        Returns:
            Comparison metrics
        """
        orig_scores, orig_labels, orig_boxes = original_result
        comp_scores, comp_labels, comp_boxes = compressed_result
        
        comparison = {
            'original_detections': len(orig_boxes),
            'compressed_detections': len(comp_boxes),
            'detection_count_diff': len(comp_boxes) - len(orig_boxes)
        }
        
        # If both have detections, compare them
        if len(orig_boxes) > 0 and len(comp_boxes) > 0:
            # Compare top detection scores
            if len(orig_scores) > 0 and len(comp_scores) > 0:
                comparison['top_score_original'] = float(orig_scores[0])
                comparison['top_score_compressed'] = float(comp_scores[0])
                comparison['score_difference'] = float(comp_scores[0] - orig_scores[0])
            
            # Compare bounding box positions (simplified)
            if len(orig_boxes) > 0 and len(comp_boxes) > 0:
                orig_center = [(box[0] + box[2])/2 for box in orig_boxes[:3]]  # Top 3 boxes
                comp_center = [(box[0] + box[2])/2 for box in comp_boxes[:3]]  # Top 3 boxes
                
                if orig_center and comp_center:
                    comparison['avg_center_x_original'] = np.mean(orig_center)
                    comparison['avg_center_x_compressed'] = np.mean(comp_center)
        
        return comparison
    
    def run_demo(self, image_path: str, original_model_path: str, compressed_model_path: str):
        """
        Run the complete demonstration.
        
        Args:
            image_path: Path to test image
            original_model_path: Path to original ONNX model
            compressed_model_path: Path to compressed ONNX model
        """
        print("="*60)
        print("RF-DETR COMPRESSED MODEL DEMONSTRATION")
        print("="*60)
        
        # Check if files exist
        if not os.path.exists(image_path):
            print(f"✗ Image not found: {image_path}")
            return
            
        if not os.path.exists(original_model_path):
            print(f"✗ Original model not found: {original_model_path}")
            return
            
        if not os.path.exists(compressed_model_path):
            print(f"✗ Compressed model not found: {compressed_model_path}")
            return
        
        # Load models
        original_model, compressed_model = self.load_models(original_model_path, compressed_model_path)
        
        if original_model is None or compressed_model is None:
            print("✗ Failed to load models. Cannot proceed with demo.")
            return
        
        # Model size comparison
        print("\n" + "-"*40)
        print("MODEL SIZE COMPARISON")
        print("-"*40)
        
        try:
            size_comparison = self.evaluator.compare_models(original_model_path, compressed_model_path)
            orig_size = size_comparison['original']['size']['file_size_mb']
            comp_size = size_comparison['compressed']['size']['file_size_mb']
            reduction = size_comparison['improvements']['size_reduction_ratio'] * 100
            
            print(f"Original model size: {orig_size:.2f} MB")
            print(f"Compressed model size: {comp_size:.2f} MB")
            print(f"Size reduction: {reduction:.1f}%")
            
        except Exception as e:
            print(f"⚠ Size comparison failed: {e}")
        
        # Performance benchmarking
        print("\n" + "-"*40)
        print("PERFORMANCE BENCHMARKING")
        print("-"*40)
        
        # Benchmark original model
        print("Benchmarking original model...")
        original_perf = self.benchmark_inference(original_model, image_path, num_runs=20)
        
        # Benchmark compressed model
        print("Benchmarking compressed model...")
        compressed_perf = self.benchmark_inference(compressed_model, image_path, num_runs=20)
        
        # Display performance results
        print(f"\nOriginal Model Performance:")
        print(f"  Average inference time: {original_perf['avg_time_ms']:.2f} ± {original_perf.get('std_time_ms', 0):.2f} ms")
        print(f"  FPS: {original_perf['fps']:.1f}")
        print(f"  Successful runs: {original_perf['successful_runs']}/{original_perf['total_runs']}")
        
        print(f"\nCompressed Model Performance:")
        print(f"  Average inference time: {compressed_perf['avg_time_ms']:.2f} ± {compressed_perf.get('std_time_ms', 0):.2f} ms")
        print(f"  FPS: {compressed_perf['fps']:.1f}")
        print(f"  Successful runs: {compressed_perf['successful_runs']}/{compressed_perf['total_runs']}")
        
        # Performance comparison
        if original_perf['successful_runs'] > 0 and compressed_perf['successful_runs'] > 0:
            speed_improvement = (compressed_perf['fps'] / original_perf['fps'] - 1) * 100
            print(f"\nSpeed improvement: {speed_improvement:+.1f}%")
        
        # Inference quality comparison
        print("\n" + "-"*40)
        print("INFERENCE QUALITY COMPARISON")
        print("-"*40)
        
        if (original_perf.get('first_result') is not None and 
            compressed_perf.get('first_result') is not None):
            
            quality_comparison = self.compare_inference_results(
                original_perf['first_result'], 
                compressed_perf['first_result']
            )
            
            print(f"Original model detections: {quality_comparison['original_detections']}")
            print(f"Compressed model detections: {quality_comparison['compressed_detections']}")
            print(f"Detection count difference: {quality_comparison['detection_count_diff']:+d}")
            
            if 'top_score_original' in quality_comparison:
                print(f"Top detection score (original): {quality_comparison['top_score_original']:.3f}")
                print(f"Top detection score (compressed): {quality_comparison['top_score_compressed']:.3f}")
                print(f"Score difference: {quality_comparison.get('score_difference', 0):+.3f}")
        
        # Generate output images
        print("\n" + "-"*40)
        print("GENERATING OUTPUT IMAGES")
        print("-"*40)
        
        try:
            # Run inference and save results
            orig_scores, orig_labels, orig_boxes = original_model.run_inference(image_path)
            comp_scores, comp_labels, comp_boxes = compressed_model.run_inference(image_path)
            
            # Save detection results
            original_model.save_detections(image_path, orig_boxes, orig_labels, "demo_original_detections.jpg")
            compressed_model.save_detections(image_path, comp_boxes, comp_labels, "demo_compressed_detections.jpg")
            
            print("✓ Detection images saved:")
            print("  - demo_original_detections.jpg")
            print("  - demo_compressed_detections.jpg")
            
        except Exception as e:
            print(f"⚠ Failed to generate output images: {e}")
        
        print("\n" + "="*60)
        print("DEMONSTRATION COMPLETED")
        print("="*60)


def main():
    """Main function with command-line interface."""
    parser = argparse.ArgumentParser(
        description="RF-DETR Compressed Model Demonstration",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic demo with default models
  python compressed_model_demo.py --image data/1744093833400000000.png
  
  # Demo with custom models
  python compressed_model_demo.py --image test.jpg --original rf-detr-base.onnx --compressed rf-detr-pruned.onnx
        """
    )
    
    parser.add_argument('--image', required=True, help='Path to test image')
    parser.add_argument('--original', default='rf-detr-base.onnx', 
                       help='Path to original ONNX model (default: rf-detr-base.onnx)')
    parser.add_argument('--compressed', default='rf-detr-base-pruned.onnx',
                       help='Path to compressed ONNX model (default: rf-detr-base-pruned.onnx)')
    
    args = parser.parse_args()
    
    # Run demonstration
    demo = CompressedModelDemo()
    demo.run_demo(args.image, args.original, args.compressed)


if __name__ == "__main__":
    main()
